# Q精华 Hub

一个现代化的群聊精华消息展示平台，专为展示简短消息和图片内容而设计。

## 功能特性

- 🎨 **简约现代设计** - 专为短消息优化的卡片式设计
- 📱 **完美响应式** - 1-5列自适应瀑布流布局
- 🌓 **智能主题切换** - 支持明暗主题自动检测
- 🔍 **快速搜索** - 简洁的搜索界面（UI已实现）
- 📌 **优化瀑布流** - 美观的多列瀑布流展示
- 🖼️ **图片支持** - 完美展示消息中的图片内容
- ⚡ **高性能** - 基于Next.js 15和React 19构建
- 🎭 **精美动效** - 流畅的悬停和加载动画

## 技术栈

- **框架**: Next.js 15.5.2 (App Router)
- **UI库**: React 19.1.0
- **样式**: Tailwind CSS 4.1.12
- **图标**: Lucide React
- **语言**: TypeScript 5
- **字体**: Geist (Vercel字体)

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # 组件目录
│   ├── layout/           # 布局组件
│   │   ├── Header.tsx    # 顶部导航
│   │   └── MasonryGrid.tsx # 瀑布流布局
│   └── ui/               # UI组件
│       ├── EmptyState.tsx    # 空状态
│       ├── FilterDropdown.tsx # 筛选下拉框
│       ├── LoadingSpinner.tsx # 加载动画
│       ├── PinCard.tsx       # 消息卡片
│       ├── SearchBox.tsx     # 搜索框
│       └── ThemeToggle.tsx   # 主题切换
├── data/                 # 数据目录
│   └── mockData.ts       # 模拟数据
└── types/                # 类型定义
    └── index.ts          # 通用类型
```

## 开始使用

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看结果。

### 构建生产版本

```bash
npm run build
npm start
```

## 组件说明

### Header 组件
- 包含Logo、搜索框、筛选器和主题切换按钮
- 响应式设计，移动端采用垂直布局
- 支持粘性定位和毛玻璃效果

### PinCard 组件
- 专为短消息设计的简约卡片
- 包含消息内容、作者信息和时间
- 支持图片展示和优雅的悬停效果
- 移除了不必要的标签和互动数据

### MasonryGrid 组件
- 实现响应式瀑布流布局
- 智能列数调整（1-5列）
- 优化的间距和动画效果

### 主题系统
- 基于CSS变量的主题系统
- 支持系统主题偏好检测
- 平滑的主题切换动画

## 自定义配置

### 修改主题颜色
编辑 `src/app/globals.css` 中的CSS变量：

```css
:root {
  --background: #ffffff;
  --foreground: #171717;
}
```

### 添加新的筛选选项
编辑 `src/components/layout/Header.tsx` 中的 `filterOptions` 数组。

### 修改模拟数据
编辑 `src/data/mockData.ts` 文件来添加或修改精华消息数据。

## 部署

推荐使用 [Vercel](https://vercel.com) 进行部署：

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 自动部署完成

## 开发计划

- [ ] 实现搜索功能逻辑
- [ ] 实现筛选功能逻辑
- [ ] 添加无限滚动加载
- [ ] 实现消息详情页
- [ ] 添加用户认证
- [ ] 集成后端API

## 许可证

MIT License
