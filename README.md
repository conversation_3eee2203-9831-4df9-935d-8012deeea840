# Q精华 Hub

一个现代化的精华消息聚合平台，用于发现和分享有价值的内容。

## 功能特性

- 🎨 **现代化UI设计** - 采用简洁优雅的设计风格
- 📱 **响应式布局** - 完美适配桌面端和移动端
- 🌓 **明暗主题** - 支持明暗主题自动切换
- 🔍 **搜索功能** - 快速搜索精华内容（UI已实现）
- 🏷️ **分类筛选** - 按分类筛选内容（UI已实现）
- 📌 **瀑布流布局** - 美观的瀑布流展示方式
- ⚡ **高性能** - 基于Next.js 15和React 19构建

## 技术栈

- **框架**: Next.js 15.5.2 (App Router)
- **UI库**: React 19.1.0
- **样式**: Tailwind CSS 4.1.12
- **图标**: Lucide React
- **语言**: TypeScript 5
- **字体**: <PERSON>eist (Vercel字体)

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # 组件目录
│   ├── layout/           # 布局组件
│   │   ├── Header.tsx    # 顶部导航
│   │   └── MasonryGrid.tsx # 瀑布流布局
│   └── ui/               # UI组件
│       ├── EmptyState.tsx    # 空状态
│       ├── FilterDropdown.tsx # 筛选下拉框
│       ├── LoadingSpinner.tsx # 加载动画
│       ├── PinCard.tsx       # 消息卡片
│       ├── SearchBox.tsx     # 搜索框
│       └── ThemeToggle.tsx   # 主题切换
├── data/                 # 数据目录
│   └── mockData.ts       # 模拟数据
└── types/                # 类型定义
    └── index.ts          # 通用类型
```

## 开始使用

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看结果。

### 构建生产版本

```bash
npm run build
npm start
```

## 组件说明

### Header 组件
- 包含Logo、搜索框、筛选器和主题切换按钮
- 响应式设计，移动端采用垂直布局
- 支持粘性定位和毛玻璃效果

### PinCard 组件
- 展示精华消息的卡片组件
- 包含标题、内容预览、作者、标签、时间等信息
- 支持图片展示和交互数据显示

### MasonryGrid 组件
- 实现瀑布流布局
- 响应式列数调整（1-4列）
- 自动分配消息到各列

### 主题系统
- 基于CSS变量的主题系统
- 支持系统主题偏好检测
- 平滑的主题切换动画

## 自定义配置

### 修改主题颜色
编辑 `src/app/globals.css` 中的CSS变量：

```css
:root {
  --background: #ffffff;
  --foreground: #171717;
}
```

### 添加新的筛选选项
编辑 `src/components/layout/Header.tsx` 中的 `filterOptions` 数组。

### 修改模拟数据
编辑 `src/data/mockData.ts` 文件来添加或修改精华消息数据。

## 部署

推荐使用 [Vercel](https://vercel.com) 进行部署：

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 自动部署完成

## 开发计划

- [ ] 实现搜索功能逻辑
- [ ] 实现筛选功能逻辑
- [ ] 添加无限滚动加载
- [ ] 实现消息详情页
- [ ] 添加用户认证
- [ ] 集成后端API

## 许可证

MIT License
