'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import MasonryGrid from '@/components/layout/MasonryGrid';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { mockMessages } from '@/data/mockData';
import { PinMessage } from '@/types';

export default function Home() {
  const [messages, setMessages] = useState<PinMessage[]>([]);
  const [loading, setLoading] = useState(true);

  // 模拟加载数据
  useEffect(() => {
    const timer = setTimeout(() => {
      setMessages(mockMessages);
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleCardClick = (message: PinMessage) => {
    console.log('点击卡片:', message.title);
    // 这里可以添加卡片点击逻辑，比如打开详情页
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <Header />

      <main className="py-6 sm:py-8 lg:py-12 min-h-[60vh]">
        {loading ? (
          <LoadingSpinner />
        ) : (
          <MasonryGrid
            messages={messages}
            onCardClick={handleCardClick}
          />
        )}
      </main>

      <footer className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            © 2024 Q精华 Hub. 发现和分享有价值的内容.
          </p>
        </div>
      </footer>
    </div>
  );
}
