'use client';

import { useState } from 'react';
import Header from '@/components/layout/Header';
import MasonryGrid from '@/components/layout/MasonryGrid';
import { mockMessages } from '@/data/mockData';
import { PinMessage } from '@/types';

export default function Home() {
  const [messages] = useState<PinMessage[]>(mockMessages);

  const handleCardClick = (message: PinMessage) => {
    console.log('点击卡片:', message.title);
    // 这里可以添加卡片点击逻辑，比如打开详情页
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="py-8">
        <MasonryGrid
          messages={messages}
          onCardClick={handleCardClick}
        />
      </main>

      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            © 2024 Q精华 Hub. 发现和分享有价值的内容.
          </p>
        </div>
      </footer>
    </div>
  );
}
