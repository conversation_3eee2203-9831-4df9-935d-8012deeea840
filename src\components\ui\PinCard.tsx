'use client';

import { PinMessage } from '@/types';

interface PinCardProps {
  message: PinMessage;
  onClick?: () => void;
}

export default function PinCard({ message, onClick }: PinCardProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${month}-${day} 发送 · ${month}-${day} 由 丝之绕我来了 😘 设置`;
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-100 dark:border-gray-700 p-4 group"
      onClick={onClick}
    >
      {/* 头像和用户信息 */}
      <div className="flex items-start gap-3 mb-3">
        {/* 头像 */}
        <div className="flex-shrink-0">
          {message.author.avatar ? (
            <img
              src={message.author.avatar}
              alt={message.author.name}
              className="w-10 h-10 rounded-full"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium text-sm">
              {message.author.name.charAt(0)}
            </div>
          )}
        </div>

        {/* 用户信息 */}
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-white text-base mb-1">
            {message.author.name}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {formatTime(message.timestamp)}
          </p>
        </div>
      </div>

      {/* 消息内容 */}
      <div className="flex">
        <div className="w-10 flex-shrink-0"></div>
        <div className="flex-1 pl-3">
          <p className="text-gray-800 dark:text-gray-200 text-base leading-relaxed break-words">
            {message.content}
          </p>

          {/* 图片 */}
          {message.imageUrl && (
            <div className="mt-3 rounded-lg overflow-hidden">
              <img
                src={message.imageUrl}
                alt="消息图片"
                className="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-200"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
