'use client';

import { Heart, MessageCircle, Clock, User } from 'lucide-react';
import { PinMessage } from '@/types';

interface PinCardProps {
  message: PinMessage;
  onClick?: () => void;
}

export default function PinCard({ message, onClick }: PinCardProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return '刚刚';
    if (diffInHours < 24) return `${diffInHours}小时前`;
    if (diffInHours < 24 * 7) return `${Math.floor(diffInHours / 24)}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  return (
    <div 
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-200 dark:border-gray-700 overflow-hidden group"
      onClick={onClick}
    >
      {/* 图片 */}
      {message.imageUrl && (
        <div className="aspect-video w-full overflow-hidden">
          <img 
            src={message.imageUrl} 
            alt={message.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
        </div>
      )}
      
      <div className="p-4">
        {/* 标题 */}
        <h3 className="font-semibold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {message.title}
        </h3>
        
        {/* 内容预览 */}
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-3">
          {message.content}
        </p>
        
        {/* 标签 */}
        <div className="flex flex-wrap gap-2 mb-3">
          {message.tags.map((tag, index) => (
            <span 
              key={index}
              className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full"
            >
              #{tag}
            </span>
          ))}
        </div>
        
        {/* 底部信息 */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          {/* 作者和时间 */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              {message.author.avatar ? (
                <img 
                  src={message.author.avatar} 
                  alt={message.author.name}
                  className="w-5 h-5 rounded-full"
                />
              ) : (
                <User className="w-4 h-4" />
              )}
              <span className="text-xs">{message.author.name}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span className="text-xs">{formatTime(message.timestamp)}</span>
            </div>
          </div>
          
          {/* 互动数据 */}
          <div className="flex items-center gap-3">
            {message.likes !== undefined && (
              <div className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                <span className="text-xs">{message.likes}</span>
              </div>
            )}
            {message.comments !== undefined && (
              <div className="flex items-center gap-1">
                <MessageCircle className="w-4 h-4" />
                <span className="text-xs">{message.comments}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
