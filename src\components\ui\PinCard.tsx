'use client';

import { PinMessage } from '@/types';

interface PinCardProps {
  message: PinMessage;
  onClick?: () => void;
}

export default function PinCard({ message, onClick }: PinCardProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${month}-${day} 发送 · ${month}-${day} 由 丝之绕我来了 😘 设置`;
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-100 dark:border-gray-700 overflow-hidden p-5 group"
      onClick={onClick}
    >
      {/* 顶部：头像、用户名和时间信息 */}
      <div className="flex items-start gap-3 mb-4">
        {/* 头像 */}
        <div className="flex-shrink-0">
          {message.author.avatar ? (
            <img
              src={message.author.avatar}
              alt={message.author.name}
              className="w-11 h-11 rounded-full ring-2 ring-gray-100 dark:ring-gray-600"
            />
          ) : (
            <div className="w-11 h-11 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium text-sm ring-2 ring-gray-100 dark:ring-gray-600">
              {message.author.name.charAt(0)}
            </div>
          )}
        </div>

        {/* 用户信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-gray-900 dark:text-white text-base">
              {message.author.name}
            </h3>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">
            {formatTime(message.timestamp)}
          </p>
        </div>
      </div>

      {/* 消息内容 */}
      <div className="ml-[56px]">
        <p className="text-gray-800 dark:text-gray-200 text-[15px] leading-relaxed break-words mb-0">
          {message.content}
        </p>

        {/* 图片 */}
        {message.imageUrl && (
          <div className="mt-4 rounded-xl overflow-hidden shadow-sm">
            <img
              src={message.imageUrl}
              alt="消息图片"
              className="w-full h-auto object-cover group-hover:scale-[1.02] transition-transform duration-300"
            />
          </div>
        )}
      </div>
    </div>
  );
}
