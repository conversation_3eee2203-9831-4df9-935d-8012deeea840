'use client';

import { Clock, User } from 'lucide-react';
import { PinMessage } from '@/types';

interface PinCardProps {
  message: PinMessage;
  onClick?: () => void;
}

export default function PinCard({ message, onClick }: PinCardProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return '刚刚';
    if (diffInHours < 24) return `${diffInHours}小时前`;
    if (diffInHours < 24 * 7) return `${Math.floor(diffInHours / 24)}天前`;
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer border border-gray-100 dark:border-gray-700 overflow-hidden group hover:-translate-y-1"
      onClick={onClick}
    >
      {/* 图片 */}
      {message.imageUrl && (
        <div className="w-full overflow-hidden">
          <img
            src={message.imageUrl}
            alt="消息图片"
            className="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}

      <div className="p-5">
        {/* 消息内容 */}
        <p className="text-gray-800 dark:text-gray-200 text-base leading-relaxed mb-4 break-words">
          {message.content}
        </p>

        {/* 底部信息 */}
        <div className="flex items-center justify-between">
          {/* 作者信息 */}
          <div className="flex items-center gap-2">
            {message.author.avatar ? (
              <img
                src={message.author.avatar}
                alt={message.author.name}
                className="w-6 h-6 rounded-full ring-2 ring-gray-100 dark:ring-gray-600"
              />
            ) : (
              <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                <User className="w-3 h-3 text-white" />
              </div>
            )}
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {message.author.name}
            </span>
          </div>

          {/* 时间 */}
          <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
            <Clock className="w-3 h-3" />
            <span className="text-xs">{formatTime(message.timestamp)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
