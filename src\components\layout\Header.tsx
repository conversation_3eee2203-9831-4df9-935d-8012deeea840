'use client';

import SearchBox from '../ui/SearchBox';
import ThemeToggle from '../ui/ThemeToggle';

export default function Header() {
  const handleSearch = (query: string) => {
    console.log('搜索:', query);
    // 这里可以添加搜索逻辑
  };

  return (
    <header className="sticky top-0 z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 桌面端布局 */}
        <div className="hidden md:flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Q精华 Hub
            </h1>
          </div>

          {/* 搜索框 */}
          <div className="flex-1 max-w-md mx-8">
            <SearchBox
              placeholder="搜索精华消息..."
              onSearch={handleSearch}
            />
          </div>

          {/* 主题切换 */}
          <div className="flex-shrink-0">
            <ThemeToggle />
          </div>
        </div>

        {/* 移动端布局 */}
        <div className="md:hidden">
          {/* 第一行：Logo和主题切换 */}
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Q精华 Hub
            </h1>
            <ThemeToggle />
          </div>

          {/* 第二行：搜索框 */}
          <div className="pb-4">
            <SearchBox
              placeholder="搜索精华消息..."
              onSearch={handleSearch}
            />
          </div>
        </div>
      </div>
    </header>
  );
}
