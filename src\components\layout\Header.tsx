'use client';

import SearchBox from '../ui/SearchBox';
import FilterDropdown from '../ui/FilterDropdown';
import ThemeToggle from '../ui/ThemeToggle';

const filterOptions = [
  { value: 'all', label: '全部' },
  { value: 'tech', label: '技术' },
  { value: 'design', label: '设计' },
  { value: 'product', label: '产品' },
  { value: 'business', label: '商业' },
  { value: 'life', label: '生活' },
];

export default function Header() {
  const handleSearch = (query: string) => {
    console.log('搜索:', query);
    // 这里可以添加搜索逻辑
  };

  const handleFilter = (value: string) => {
    console.log('筛选:', value);
    // 这里可以添加筛选逻辑
  };

  return (
    <header className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 桌面端布局 */}
        <div className="hidden md:flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Q精华 Hub
            </h1>
          </div>

          {/* 功能区 */}
          <div className="flex items-center gap-4 flex-1 max-w-2xl mx-8">
            <SearchBox
              placeholder="搜索精华内容..."
              onSearch={handleSearch}
            />
            <FilterDropdown
              options={filterOptions}
              placeholder="筛选分类"
              onSelect={handleFilter}
            />
          </div>

          {/* 主题切换 */}
          <div className="flex-shrink-0">
            <ThemeToggle />
          </div>
        </div>

        {/* 移动端布局 */}
        <div className="md:hidden">
          {/* 第一行：Logo和主题切换 */}
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              Q精华 Hub
            </h1>
            <ThemeToggle />
          </div>

          {/* 第二行：搜索和筛选 */}
          <div className="pb-4 space-y-3">
            <SearchBox
              placeholder="搜索精华内容..."
              onSearch={handleSearch}
            />
            <div className="flex justify-center">
              <FilterDropdown
                options={filterOptions}
                placeholder="筛选分类"
                onSelect={handleFilter}
              />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
