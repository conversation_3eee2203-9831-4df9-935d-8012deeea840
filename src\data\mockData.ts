import { PinMessage } from '@/types';

export const mockMessages: PinMessage[] = [
  {
    id: '1',
    title: '你、你在说什么啊！',
    content: '这是一条来自Andrea的精华消息，表达了惊讶和困惑的情绪。',
    author: {
      name: '<PERSON> (安之若)',
      avatar: '/avatars/andrea.jpg'
    },
    tags: ['日常', '表情'],
    timestamp: '2024-08-21T10:30:00Z',
    category: 'life',
    likes: 42,
    comments: 8
  },
  {
    id: '2',
    title: '欸~ 我和哔哩哔哩哔哔并列第一耶。',
    content: 'Andrea分享了一个有趣的发现，关于排名的惊喜。',
    author: {
      name: '<PERSON> (安之若)',
      avatar: '/avatars/andrea.jpg'
    },
    tags: ['成就', '分享'],
    timestamp: '2024-08-21T09:15:00Z',
    category: 'life',
    likes: 156,
    comments: 23
  },
  {
    id: '3',
    title: 'Next.js 15 新特性深度解析',
    content: 'Next.js 15 带来了许多令人兴奋的新特性，包括改进的App Router、更好的性能优化和新的缓存策略。本文将深入探讨这些新特性如何改善开发体验。',
    author: {
      name: '前端小王',
      avatar: '/avatars/dev1.jpg'
    },
    tags: ['Next.js', '前端', '技术'],
    timestamp: '2024-08-20T14:22:00Z',
    category: 'tech',
    likes: 89,
    comments: 15,
    imageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop'
  },
  {
    id: '4',
    title: 'UI设计中的色彩心理学',
    content: '色彩在用户界面设计中扮演着至关重要的角色。不同的颜色会引发用户不同的情感反应，影响用户的行为和决策。了解色彩心理学可以帮助设计师创造更有效的用户体验。',
    author: {
      name: '设计师小李',
      avatar: '/avatars/designer1.jpg'
    },
    tags: ['UI设计', '色彩', '心理学'],
    timestamp: '2024-08-20T11:45:00Z',
    category: 'design',
    likes: 67,
    comments: 12,
    imageUrl: 'https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=400&h=250&fit=crop'
  },
  {
    id: '5',
    title: '产品经理的一天',
    content: '作为产品经理，每天都充满了挑战和机遇。从需求分析到用户调研，从原型设计到数据分析，每一个环节都需要细致的思考和规划。',
    author: {
      name: '产品老张',
      avatar: '/avatars/pm1.jpg'
    },
    tags: ['产品管理', '工作日常'],
    timestamp: '2024-08-19T16:30:00Z',
    category: 'product',
    likes: 34,
    comments: 7
  },
  {
    id: '6',
    title: 'React 19 的并发特性详解',
    content: 'React 19 引入了更强大的并发特性，包括自动批处理、Suspense改进和新的Hook。这些特性将大大提升应用的性能和用户体验。让我们深入了解这些变化。',
    author: {
      name: 'React专家',
      avatar: '/avatars/react-expert.jpg'
    },
    tags: ['React', '并发', '性能优化'],
    timestamp: '2024-08-19T13:20:00Z',
    category: 'tech',
    likes: 128,
    comments: 31,
    imageUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=280&fit=crop'
  },
  {
    id: '7',
    title: '极简主义设计的力量',
    content: '少即是多。极简主义设计通过减少不必要的元素，突出核心内容，创造出清晰、优雅的用户界面。这种设计哲学不仅美观，更能提升用户的使用效率。',
    author: {
      name: '简约设计师',
      avatar: '/avatars/minimalist.jpg'
    },
    tags: ['极简设计', '用户体验'],
    timestamp: '2024-08-18T20:15:00Z',
    category: 'design',
    likes: 92,
    comments: 18,
    imageUrl: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=400&h=320&fit=crop'
  },
  {
    id: '8',
    title: '创业公司的技术选型思考',
    content: '在创业初期，技术选型至关重要。需要在快速迭代、团队技能、长期维护等多个维度进行权衡。本文分享一些实用的选型经验和思考框架。',
    author: {
      name: '创业者小陈',
      avatar: '/avatars/startup.jpg'
    },
    tags: ['创业', '技术选型', '团队管理'],
    timestamp: '2024-08-18T15:40:00Z',
    category: 'business',
    likes: 76,
    comments: 22
  },
  {
    id: '9',
    title: '咖啡与代码的完美结合',
    content: '作为程序员，咖啡已经成为我们工作中不可或缺的伙伴。不同的咖啡豆、冲泡方式都会带来不同的体验，就像编程语言一样，各有特色。',
    author: {
      name: '咖啡程序员',
      avatar: '/avatars/coffee-dev.jpg'
    },
    tags: ['咖啡', '程序员', '生活'],
    timestamp: '2024-08-17T09:25:00Z',
    category: 'life',
    likes: 45,
    comments: 9,
    imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop'
  },
  {
    id: '10',
    title: 'TypeScript 5.0 新特性一览',
    content: 'TypeScript 5.0 带来了许多激动人心的新特性，包括装饰器支持、const断言改进、以及更好的类型推断。这些改进将让我们的代码更加类型安全和易于维护。',
    author: {
      name: 'TS爱好者',
      avatar: '/avatars/ts-lover.jpg'
    },
    tags: ['TypeScript', '类型安全', '开发工具'],
    timestamp: '2024-08-16T14:10:00Z',
    category: 'tech',
    likes: 103,
    comments: 26
  }
];
