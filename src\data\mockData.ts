import { PinMessage } from '@/types';

export const mockMessages: PinMessage[] = [
  {
    id: '1',
    content: '你、你在说什么啊！',
    author: {
      name: '<PERSON> (安之若)',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-21T10:30:00Z'
  },
  {
    id: '2',
    content: '欸~ 我和哔哩哔哩哔哔并列第一耶。',
    author: {
      name: '<PERSON> (安之若)',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-21T09:15:00Z'
  },
  {
    id: '2.5',
    content: '本小姐的时间可是很宝贵的，不是全球可以省着的！',
    author: {
      name: '<PERSON> (安之若)',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-19T15:20:00Z'
  },
  {
    id: '3',
    content: '今天的代码写得特别顺手，感觉自己就是天才程序员！',
    author: {
      name: '前端小王',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-20T14:22:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop'
  },
  {
    id: '4',
    content: '这个配色方案真的太美了，忍不住要分享给大家看看！',
    author: {
      name: '设计师小李',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-20T11:45:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=400&h=250&fit=crop'
  },
  {
    id: '5',
    content: '今天开会讨论新功能，大家的想法都很棒，期待产品上线！',
    author: {
      name: '产品老张',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-19T16:30:00Z'
  },
  {
    id: '5.5',
    content: '哈哈哈，这个bug改了一天终于搞定了！',
    author: {
      name: '后端小刘',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-19T18:45:00Z'
  },
  {
    id: '6',
    content: 'React 19 真的太强了！并发特性让我的应用性能提升了好多',
    author: {
      name: 'React专家',
      avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-19T13:20:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=280&fit=crop'
  },
  {
    id: '7',
    content: '少即是多，这个设计理念永远不会过时',
    author: {
      name: '简约设计师',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-18T20:15:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=400&h=320&fit=crop'
  },
  {
    id: '8',
    content: '创业路上的每一个技术决策都很关键，要慎重考虑',
    author: {
      name: '创业者小陈',
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-18T15:40:00Z'
  },
  {
    id: '8.5',
    content: '周末加班写代码，但是看到功能上线的那一刻还是很有成就感的！',
    author: {
      name: '全栈工程师',
      avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-18T22:30:00Z'
  },
  {
    id: '9',
    content: '一杯好咖啡配上优雅的代码，这就是程序员的浪漫',
    author: {
      name: '咖啡程序员',
      avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-17T09:25:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop'
  },
  {
    id: '10',
    content: 'TypeScript 让我的代码更安全了，再也不怕类型错误！',
    author: {
      name: 'TS爱好者',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-16T14:10:00Z'
  },
  {
    id: '11',
    content: '今天学会了一个新的CSS技巧，感觉自己又进步了一点点',
    author: {
      name: 'CSS小能手',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-15T16:45:00Z'
  },
  {
    id: '12',
    content: '这张照片的光影效果太棒了！',
    author: {
      name: '摄影爱好者',
      avatar: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-15T11:20:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=500&fit=crop'
  },
  {
    id: '13',
    content: '周末的阳光真好，适合出去走走',
    author: {
      name: '阳光少女',
      avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-14T10:30:00Z'
  },
  {
    id: '14',
    content: '刚做的这道菜卖相还不错，味道也很棒！',
    author: {
      name: '美食达人',
      avatar: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-13T19:15:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=350&fit=crop'
  },
  {
    id: '15',
    content: '今天的健身训练特别有效果，感觉浑身充满力量',
    author: {
      name: '健身教练',
      avatar: 'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-13T07:30:00Z'
  },
  {
    id: '16',
    content: '哈哈哈，刚才那个表情包太搞笑了！',
    author: {
      name: '表情包收集者',
      avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-12T20:15:00Z'
  },
  {
    id: '17',
    content: '明天有谁要一起去看电影吗？新上映的那部科幻片评价很不错',
    author: {
      name: '电影爱好者',
      avatar: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-12T16:45:00Z'
  },
  {
    id: '18',
    content: '刚刚路过那家新开的奶茶店，排队的人好多啊！',
    author: {
      name: '奶茶控',
      avatar: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=100&h=100&fit=crop&crop=face'
    },
    timestamp: '2024-08-12T14:20:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop'
  }
];
