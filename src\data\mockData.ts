import { PinMessage } from '@/types';

export const mockMessages: PinMessage[] = [
  {
    id: '1',
    content: '你、你在说什么啊！',
    author: {
      name: '<PERSON> (安之若)',
      avatar: '/avatars/andrea.jpg'
    },
    timestamp: '2024-08-21T10:30:00Z'
  },
  {
    id: '2',
    content: '欸~ 我和哔哩哔哩哔哔并列第一耶。',
    author: {
      name: '<PERSON> (安之若)',
      avatar: '/avatars/andrea.jpg'
    },
    timestamp: '2024-08-21T09:15:00Z'
  },
  {
    id: '3',
    content: '今天的代码写得特别顺手，感觉自己就是天才程序员！',
    author: {
      name: '前端小王',
      avatar: '/avatars/dev1.jpg'
    },
    timestamp: '2024-08-20T14:22:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop'
  },
  {
    id: '4',
    content: '这个配色方案真的太美了，忍不住要分享给大家看看！',
    author: {
      name: '设计师小李',
      avatar: '/avatars/designer1.jpg'
    },
    timestamp: '2024-08-20T11:45:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=400&h=250&fit=crop'
  },
  {
    id: '5',
    content: '今天开会讨论新功能，大家的想法都很棒，期待产品上线！',
    author: {
      name: '产品老张',
      avatar: '/avatars/pm1.jpg'
    },
    timestamp: '2024-08-19T16:30:00Z'
  },
  {
    id: '6',
    content: 'React 19 真的太强了！并发特性让我的应用性能提升了好多',
    author: {
      name: 'React专家',
      avatar: '/avatars/react-expert.jpg'
    },
    timestamp: '2024-08-19T13:20:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=280&fit=crop'
  },
  {
    id: '7',
    content: '少即是多，这个设计理念永远不会过时',
    author: {
      name: '简约设计师',
      avatar: '/avatars/minimalist.jpg'
    },
    timestamp: '2024-08-18T20:15:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=400&h=320&fit=crop'
  },
  {
    id: '8',
    content: '创业路上的每一个技术决策都很关键，要慎重考虑',
    author: {
      name: '创业者小陈',
      avatar: '/avatars/startup.jpg'
    },
    timestamp: '2024-08-18T15:40:00Z'
  },
  {
    id: '9',
    content: '一杯好咖啡配上优雅的代码，这就是程序员的浪漫',
    author: {
      name: '咖啡程序员',
      avatar: '/avatars/coffee-dev.jpg'
    },
    timestamp: '2024-08-17T09:25:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop'
  },
  {
    id: '10',
    content: 'TypeScript 让我的代码更安全了，再也不怕类型错误！',
    author: {
      name: 'TS爱好者',
      avatar: '/avatars/ts-lover.jpg'
    },
    timestamp: '2024-08-16T14:10:00Z'
  },
  {
    id: '11',
    content: '今天学会了一个新的CSS技巧，感觉自己又进步了一点点',
    author: {
      name: 'CSS小能手',
      avatar: '/avatars/css-master.jpg'
    },
    timestamp: '2024-08-15T16:45:00Z'
  },
  {
    id: '12',
    content: '这张照片的光影效果太棒了！',
    author: {
      name: '摄影爱好者',
      avatar: '/avatars/photographer.jpg'
    },
    timestamp: '2024-08-15T11:20:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=500&fit=crop'
  },
  {
    id: '13',
    content: '周末的阳光真好，适合出去走走',
    author: {
      name: '阳光少女',
      avatar: '/avatars/sunshine.jpg'
    },
    timestamp: '2024-08-14T10:30:00Z'
  },
  {
    id: '14',
    content: '刚做的这道菜卖相还不错，味道也很棒！',
    author: {
      name: '美食达人',
      avatar: '/avatars/chef.jpg'
    },
    timestamp: '2024-08-13T19:15:00Z',
    imageUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=350&fit=crop'
  },
  {
    id: '15',
    content: '今天的健身训练特别有效果，感觉浑身充满力量',
    author: {
      name: '健身教练',
      avatar: '/avatars/trainer.jpg'
    },
    timestamp: '2024-08-13T07:30:00Z'
  }
];
