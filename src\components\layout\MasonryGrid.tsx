'use client';

import { useState, useEffect, useRef } from 'react';
import { PinMessage } from '@/types';
import PinCard from '../ui/PinCard';

interface MasonryGridProps {
  messages: PinMessage[];
  onCardClick?: (message: PinMessage) => void;
}

export default function MasonryGrid({ messages, onCardClick }: MasonryGridProps) {
  const [columns, setColumns] = useState(3);
  const containerRef = useRef<HTMLDivElement>(null);

  // 响应式列数计算
  useEffect(() => {
    const updateColumns = () => {
      if (!containerRef.current) return;
      
      const width = containerRef.current.offsetWidth;
      if (width < 640) setColumns(1);      // sm
      else if (width < 1024) setColumns(2); // md
      else if (width < 1280) setColumns(3); // lg
      else setColumns(4);                   // xl+
    };

    updateColumns();
    window.addEventListener('resize', updateColumns);
    return () => window.removeEventListener('resize', updateColumns);
  }, []);

  // 将消息分配到各列
  const distributeMessages = () => {
    const columnArrays: PinMessage[][] = Array.from({ length: columns }, () => []);
    
    messages.forEach((message, index) => {
      const columnIndex = index % columns;
      columnArrays[columnIndex].push(message);
    });
    
    return columnArrays;
  };

  const columnArrays = distributeMessages();

  return (
    <div 
      ref={containerRef}
      className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
    >
      <div className="flex gap-4">
        {columnArrays.map((columnMessages, columnIndex) => (
          <div 
            key={columnIndex}
            className="flex-1 space-y-4"
          >
            {columnMessages.map((message) => (
              <PinCard
                key={message.id}
                message={message}
                onClick={() => onCardClick?.(message)}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
